.file-upload {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.file-upload form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.file-input-container {
  position: relative;
  width: 100%;
}

.file-input {
  position: absolute;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  z-index: -1;
}

.file-label {
  display: block;
  width: 100%;
  padding: 1rem;
  background-color: #2a2a2a;
  border: 2px dashed #646cff;
  border-radius: 8px;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-label:hover {
  background-color: #333;
  border-color: #535bf2;
}

.upload-button {
  width: 100%;
  padding: 0.8rem;
  background-color: #646cff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.upload-button:hover:not(:disabled) {
  background-color: #535bf2;
}

.upload-button:disabled {
  background-color: #444;
  cursor: not-allowed;
  opacity: 0.7;
}
