from rest_framework import serializers
from .models import AudioFile, Prediction

class AudioFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = AudioFile
        fields = ['id', 'file', 'uploaded_at']

class PredictionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Prediction
        fields = ['id', 'audio_file', 'genre', 'confidence', 'created_at']

class TopPredictionSerializer(serializers.Serializer):
    genre = serializers.CharField(max_length=100)
    confidence = serializers.FloatField()

class PredictionResultSerializer(serializers.Serializer):
    genre = serializers.CharField(max_length=100)
    confidence = serializers.FloatField()
    top_predictions = TopPredictionSerializer(many=True, required=False)
