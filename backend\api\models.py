from django.db import models

class AudioFile(models.Model):
    """Model for storing uploaded audio files"""
    file = models.FileField(upload_to='uploads/')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Audio file {self.id} uploaded at {self.uploaded_at}"

class Prediction(models.Model):
    """Model for storing prediction results"""
    audio_file = models.ForeignKey(AudioFile, on_delete=models.CASCADE, related_name='predictions')
    genre = models.CharField(max_length=100)
    confidence = models.FloatField()
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Prediction: {self.genre} ({self.confidence:.2f})"
