document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const uploadForm = document.getElementById('upload-form');
    const fileInput = document.getElementById('file-input');
    const fileLabel = document.querySelector('.file-label');
    const uploadButton = document.querySelector('.upload-button');
    const loadingElement = document.getElementById('loading');
    const errorElement = document.getElementById('error');
    const resultsElement = document.getElementById('results');
    const genreNameElement = document.getElementById('genre-name');
    const genreIconElement = document.getElementById('genre-icon');
    const confidenceValueElement = document.getElementById('confidence-value');
    const confidenceBarElement = document.getElementById('confidence-bar');
    const resultExplanationElement = document.getElementById('result-explanation');
    const tryAgainButton = document.getElementById('try-again');
    const fileInfoElement = document.getElementById('file-info');
    const fileNameElement = document.getElementById('file-name');
    const fileSizeElement = document.getElementById('file-size');
    const clearFileButton = document.getElementById('clear-file');
    const resultsTimestampElement = document.getElementById('results-timestamp');
    const shareResultsButton = document.getElementById('share-results');

    // Modal elements
    const aboutButton = document.getElementById('about-button');
    const helpButton = document.getElementById('help-button');
    const aboutModal = document.getElementById('about-modal');
    const helpModal = document.getElementById('help-modal');
    const closeModalButtons = document.querySelectorAll('.close-modal');

    // Genre icons mapping
    const genreIcons = {
        'rock': '🎸',
        'pop': '🎤',
        'hiphop': '🎧',
        'jazz': '🎷',
        'classical': '🎻',
        'country': '🤠',
        'metal': '🤘',
        'blues': '🎺',
        'reggae': '🌴',
        'disco': '🕺'
    };

    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Handle file selection
    fileInput.addEventListener('change', function() {
        if (fileInput.files.length > 0) {
            const file = fileInput.files[0];
            const fileName = file.name;
            const fileSize = formatFileSize(file.size);

            // Update file label
            fileLabel.textContent = 'File selected';

            // Show file info
            fileNameElement.textContent = fileName;
            fileSizeElement.textContent = fileSize;
            fileInfoElement.style.display = 'block';

            // Enable upload button
            uploadButton.disabled = false;

            // Hide previous results and errors
            resultsElement.style.display = 'none';
            errorElement.style.display = 'none';
        } else {
            resetFileInput();
        }
    });

    // Clear file selection
    clearFileButton.addEventListener('click', function() {
        resetFileInput();
    });

    // Reset file input
    function resetFileInput() {
        fileInput.value = '';
        fileLabel.textContent = 'Choose an audio file';
        fileInfoElement.style.display = 'none';
        uploadButton.disabled = true;
    }

    // Try again button
    tryAgainButton.addEventListener('click', function() {
        resetFileInput();
        resultsElement.style.display = 'none';
    });

    // Handle form submission
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!fileInput.files.length) {
            showError('Please select a file to upload');
            return;
        }

        const file = fileInput.files[0];

        // Check file type
        const validTypes = ['audio/mpeg', 'audio/wav', 'audio/x-wav', 'audio/mp3', 'audio/x-m4a', 'audio/m4a', 'audio/x-hx-aac-adts'];
        if (!validTypes.includes(file.type) && !file.name.endsWith('.mp3') && !file.name.endsWith('.wav') && !file.name.endsWith('.m4a')) {
            showError('Please upload a valid audio file (MP3, WAV, M4A)');
            return;
        }

        // Create form data
        const formData = new FormData();
        formData.append('file', file);

        // Show loading indicator
        loadingElement.style.display = 'block';
        errorElement.style.display = 'none';
        resultsElement.style.display = 'none';

        // Send the file to the API
        fetch('/api/upload/', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Server error: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // Hide loading indicator
            loadingElement.style.display = 'none';

            // Display the results
            displayResults(data);
        })
        .catch(error => {
            // Hide loading indicator
            loadingElement.style.display = 'none';

            // Show error message
            showError('Error: ' + error.message);
            console.error('Error:', error);
        });
    });

    // Function to display results
    function displayResults(result) {
        const { genre, confidence } = result;

        // Format confidence as percentage
        const confidencePercent = (confidence * 100).toFixed(2);

        // Determine confidence level class
        let confidenceClass = 'low';
        if (confidence >= 0.7) {
            confidenceClass = 'high';
        } else if (confidence >= 0.4) {
            confidenceClass = 'medium';
        }

        // Get genre icon
        const genreIcon = genreIcons[genre.toLowerCase()] || '🎵';

        // Update the UI
        genreNameElement.textContent = genre;
        genreIconElement.textContent = genreIcon;
        confidenceValueElement.textContent = confidencePercent + '%';
        confidenceValueElement.className = 'confidence-value ' + confidenceClass;

        // Animate confidence bar
        confidenceBarElement.style.width = '0%';
        confidenceBarElement.className = 'confidence-bar ' + confidenceClass;

        // Use setTimeout to create animation effect
        setTimeout(() => {
            confidenceBarElement.style.width = confidencePercent + '%';
        }, 100);

        // Update explanation text
        let explanationText = `The model predicts this audio is <strong>${genre}</strong> music with a confidence of <strong>${confidencePercent}%</strong>.`;

        // Add confidence level description
        if (confidenceClass === 'high') {
            explanationText += ' This is a high confidence prediction.';
        } else if (confidenceClass === 'medium') {
            explanationText += ' This is a moderate confidence prediction.';
        } else {
            explanationText += ' This prediction has low confidence and may not be accurate.';
        }

        resultExplanationElement.innerHTML = explanationText;

        // Show the results
        resultsElement.style.display = 'block';

        // Scroll to results
        resultsElement.scrollIntoView({ behavior: 'smooth' });
    }

    // Function to show error message
    function showError(message) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }

    // Add hover effect to genre tags
    const genreTags = document.querySelectorAll('.genre-tag');
    genreTags.forEach(tag => {
        tag.addEventListener('mouseenter', function() {
            const genre = this.textContent.toLowerCase();
            const icon = genreIcons[genre] || '🎵';
            this.setAttribute('data-original-text', this.textContent);
            this.textContent = icon;
        });

        tag.addEventListener('mouseleave', function() {
            this.textContent = this.getAttribute('data-original-text');
        });
    });

    // Modal functionality
    aboutButton.addEventListener('click', function() {
        aboutModal.style.display = 'flex';
    });

    helpButton.addEventListener('click', function() {
        helpModal.style.display = 'flex';
    });

    closeModalButtons.forEach(button => {
        button.addEventListener('click', function() {
            aboutModal.style.display = 'none';
            helpModal.style.display = 'none';
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === aboutModal) {
            aboutModal.style.display = 'none';
        }
        if (event.target === helpModal) {
            helpModal.style.display = 'none';
        }
    });

    // Share results functionality
    shareResultsButton.addEventListener('click', function() {
        // Get the prediction result
        const genre = genreNameElement.textContent;
        const confidence = confidenceValueElement.textContent;

        // Create share text
        const shareText = `I just analyzed a song with Music Genre Classifier! It's ${genre} music with ${confidence} confidence. Try it yourself!`;

        // Check if Web Share API is available
        if (navigator.share) {
            navigator.share({
                title: 'Music Genre Classification Result',
                text: shareText,
                url: window.location.href
            })
            .catch(error => console.log('Error sharing:', error));
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareText)
                .then(() => {
                    alert('Result copied to clipboard!');
                })
                .catch(err => {
                    console.error('Could not copy text: ', err);
                });
        }
    });

    // Add timestamp to results
    function updateResultsTimestamp() {
        const now = new Date();
        const options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        resultsTimestampElement.textContent = `Analyzed on ${now.toLocaleDateString('en-US', options)}`;
    }

    // Update displayResults function to include timestamp
    const originalDisplayResults = displayResults;
    displayResults = function(result) {
        originalDisplayResults(result);
        updateResultsTimestamp();
    };
});
