/* Base styles */
:root {
    /* Primary colors */
    --primary-color: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #3a0ca3;

    /* Secondary colors */
    --secondary-color: #f72585;
    --secondary-light: #ff4d6d;
    --secondary-dark: #7209b7;

    /* Neutral colors */
    --bg-dark: #0f172a;
    --bg-medium: #1e293b;
    --bg-light: #334155;
    --text-light: #f8fafc;
    --text-muted: #94a3b8;

    /* Accent colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;

    /* Fonts */
    font-family: 'Poppins', Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;
    color-scheme: light dark;
    color: var(--text-light);
    background-color: var(--bg-dark);
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    margin: 0;
    display: flex;
    place-items: center;
    min-width: 320px;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-medium) 100%);
    position: relative;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at center, transparent 0%, var(--bg-dark) 70%);
    opacity: 0.7;
    z-index: -1;
}

body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    z-index: -1;
    pointer-events: none;
}

/* App container */
.app-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0;
    text-align: center;
    width: 100%;
    display: grid;
    grid-template-rows: auto 1fr auto;
    min-height: 90vh;
    background-color: rgba(var(--bg-medium), 0.8);
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    overflow: hidden;
    position: relative;
}

.app-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    z-index: 1;
}

/* Header */
header {
    padding: 2.5rem 2rem 1.5rem;
    position: relative;
    background-color: rgba(15, 23, 42, 0.6);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: -1;
}

/* Logo */
.logo-container {
    margin-bottom: 1.5rem;
}

.logo {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 50px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.logo-icon {
    font-size: 1.5rem;
    filter: drop-shadow(0 0 5px rgba(67, 97, 238, 0.5));
}

.logo-text {
    font-weight: 700;
    font-size: 1.2rem;
    background: linear-gradient(90deg, var(--primary-light), var(--secondary-light));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    letter-spacing: 0.5px;
}

header h1 {
    font-size: 2.8rem;
    margin-bottom: 0.8rem;
    background: linear-gradient(90deg, var(--primary-light), var(--secondary-light));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    font-weight: 800;
    letter-spacing: -0.5px;
    position: relative;
    display: inline-block;
}

header h1::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-light), var(--secondary-light));
    border-radius: 3px;
}

header p {
    color: var(--text-muted);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 1rem auto 0;
    line-height: 1.6;
}

/* Header actions */
.header-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.action-button {
    background-color: rgba(15, 23, 42, 0.4);
    color: var(--text-light);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-button:hover {
    background-color: rgba(15, 23, 42, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.info-button {
    border-left: 3px solid var(--primary-light);
}

.help-button {
    border-left: 3px solid var(--secondary-light);
}

/* Main content */
main {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2.5rem;
    width: 100%;
    position: relative;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--bg-medium);
}

main::-webkit-scrollbar {
    width: 8px;
}

main::-webkit-scrollbar-track {
    background: var(--bg-medium);
}

main::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border-radius: 20px;
}

/* Features section */
.features-section {
    width: 100%;
    max-width: 800px;
    margin: 2rem auto 0;
    padding: 2rem;
    background-color: rgba(15, 23, 42, 0.3);
    border-radius: 16px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.features-section h3 {
    font-size: 1.5rem;
    color: var(--text-light);
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
    display: inline-block;
}

.features-section h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-light), var(--secondary-light));
    border-radius: 2px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.feature-card {
    background-color: rgba(30, 41, 59, 0.4);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border-color: rgba(var(--primary-light), 0.3);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.feature-card h4 {
    font-size: 1.2rem;
    margin-bottom: 0.8rem;
    color: var(--text-light);
}

.feature-card p {
    font-size: 0.9rem;
    color: var(--text-muted);
    line-height: 1.5;
}

/* Modal dialogs */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--bg-medium);
    border-radius: 16px;
    padding: 2rem;
    max-width: 600px;
    width: 90%;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
}

.close-modal {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--text-light);
}

.modal-content h2 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: var(--text-light);
    position: relative;
    display: inline-block;
}

.modal-content h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-light), var(--secondary-light));
    border-radius: 2px;
}

.modal-content p {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: var(--text-muted);
}

.modal-content ol {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.modal-content li {
    margin-bottom: 0.8rem;
    color: var(--text-muted);
}

.modal-content strong {
    color: var(--text-light);
    font-weight: 600;
}

/* Results timestamp and actions */
.results-header {
    position: relative;
    margin-bottom: 2rem;
}

.results-timestamp {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 0.5rem;
}

.results-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    justify-content: center;
}

.share-button {
    padding: 0.8rem 1.2rem;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.share-button:hover {
    background-color: var(--secondary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(247, 37, 133, 0.3);
}

/* Loading spinner and details */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(67, 97, 238, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--primary-color);
    margin: 0 auto 1rem;
    animation: spin 1s linear infinite;
}

.loading-details {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-top: 0.8rem;
}

/* File info header */
.file-info-header {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

/* Footer */
footer {
    padding: 2rem;
    font-size: 0.9rem;
    color: var(--text-muted);
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    background-color: rgba(15, 23, 42, 0.6);
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.3;
    z-index: -1;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: space-between;
}

.footer-section {
    flex: 1;
    min-width: 200px;
}

.footer-section h4 {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: var(--text-light);
    position: relative;
    display: inline-block;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-light), var(--secondary-light));
    border-radius: 2px;
}

.footer-section p {
    line-height: 1.6;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--text-muted);
    text-decoration: none;
    transition: color 0.3s ease;
    display: inline-block;
    position: relative;
}

.footer-links a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--primary-light), var(--secondary-light));
    transition: width 0.3s ease;
}

.footer-links a:hover {
    color: var(--text-light);
}

.footer-links a:hover::after {
    width: 100%;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    padding-top: 1.5rem;
    text-align: center;
}

.copyright {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.4);
    margin-top: 0.5rem;
}

/* File info */
.file-details {
    background-color: rgba(42, 42, 42, 0.5);
    padding: 0.8rem;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    text-align: left;
    font-size: 0.9rem;
}

#file-name {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#file-size {
    color: #aaa;
}

.clear-button {
    background-color: transparent;
    color: #ff6b6b;
    border: 1px solid #ff6b6b;
    border-radius: 8px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.clear-button:hover {
    background-color: rgba(255, 107, 107, 0.1);
}

/* Button icon */
.button-icon {
    margin-right: 0.5rem;
}

/* Section header */
.section-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-light);
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-light), var(--secondary-light));
    border-radius: 2px;
}

.section-description {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* File upload section */
.file-upload {
    width: 100%;
    max-width: 550px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
    background: rgba(30, 41, 59, 0.4);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.file-upload::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 20px;
    z-index: -1;
    opacity: 0.1;
    filter: blur(10px);
}

.file-upload form {
    display: flex;
    flex-direction: column;
    gap: 1.8rem;
}

.file-input-container {
    position: relative;
    width: 100%;
}

.file-input {
    position: absolute;
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    z-index: -1;
}

.file-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 2.5rem 1.5rem;
    background-color: rgba(15, 23, 42, 0.3);
    border: 2px dashed var(--primary-light);
    border-radius: 12px;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.file-label::before {
    content: '🎵';
    font-size: 3rem;
    margin-bottom: 1.2rem;
    filter: drop-shadow(0 0 10px rgba(67, 97, 238, 0.5));
}

.file-label::after {
    content: 'Drag & drop or click to browse';
    display: block;
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-top: 0.5rem;
}

.file-label:hover {
    background-color: rgba(15, 23, 42, 0.5);
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Upload button */
.upload-button {
    width: 100%;
    padding: 1.2rem;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 1.1rem;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
    position: relative;
    overflow: hidden;
}

.upload-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s ease;
}

.upload-button:hover:not(:disabled)::before {
    left: 100%;
}

.upload-button:hover:not(:disabled) {
    background: linear-gradient(90deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(67, 97, 238, 0.4);
}

.upload-button:active:not(:disabled) {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(67, 97, 238, 0.4);
}

.upload-button:disabled {
    background: var(--bg-light);
    cursor: not-allowed;
    opacity: 0.6;
    box-shadow: none;
}

/* Try again button */
.try-again-button {
    margin-top: 1.5rem;
    padding: 0.8rem 1.2rem;
    background-color: transparent;
    color: var(--primary-light);
    border: 1px solid var(--primary-light);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.try-again-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: rgba(67, 97, 238, 0.1);
    transition: all 0.3s ease;
    z-index: -1;
}

.try-again-button:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.try-again-button:hover::before {
    width: 100%;
}

/* Results container */
.results-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    padding: 2.5rem;
    background-color: rgba(30, 41, 59, 0.4);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    animation: fadeIn 0.5s ease-out;
    position: relative;
    overflow: hidden;
}

.results-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.results-container h2 {
    margin-top: 0;
    margin-bottom: 2rem;
    color: var(--text-light);
    text-align: center;
    font-size: 2rem;
    position: relative;
    font-weight: 700;
}

.results-container h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-light), var(--secondary-light));
    border-radius: 3px;
}

.result-card {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-top: 2rem;
}

.genre-result, .confidence-result {
    padding: 1.8rem;
    background-color: rgba(15, 23, 42, 0.5);
    border-radius: 16px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
}

.genre-result::before, .confidence-result::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(67, 97, 238, 0.1), transparent 70%);
    z-index: -1;
}

.genre-result:hover, .confidence-result:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
}

.genre-result h3, .confidence-result h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 600;
}

.genre-name {
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-light);
    text-transform: capitalize;
    margin: 0.5rem 0;
    background: linear-gradient(90deg, var(--primary-light), var(--secondary-light));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    display: inline-block;
}

/* Genre icon */
.genre-icon {
    font-size: 3.5rem;
    margin: 1rem 0;
    filter: drop-shadow(0 0 10px rgba(67, 97, 238, 0.3));
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Supported genres */
.supported-genres {
    margin-top: 3rem;
    width: 100%;
    max-width: 700px;
    background-color: rgba(30, 41, 59, 0.3);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.supported-genres h3 {
    font-size: 1.2rem;
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.supported-genres h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-light), var(--secondary-light));
    border-radius: 2px;
}

.genre-list {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.genre-tag {
    background-color: rgba(15, 23, 42, 0.5);
    color: var(--text-light);
    padding: 0.7rem 1.2rem;
    border-radius: 30px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.genre-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.2), rgba(247, 37, 133, 0.2));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.genre-tag:hover {
    transform: translateY(-3px) scale(1.05);
    border-color: var(--primary-light);
    color: var(--text-light);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.genre-tag:hover::before {
    opacity: 1;
}

/* File upload */
.file-upload {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.file-upload form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.file-input-container {
    position: relative;
    width: 100%;
}

.file-input {
    position: absolute;
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    z-index: -1;
}

.file-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 2rem 1rem;
    background-color: rgba(42, 42, 42, 0.5);
    border: 2px dashed #646cff;
    border-radius: 12px;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.file-label::before {
    content: '🎵';
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.file-label:hover {
    background-color: rgba(51, 51, 51, 0.7);
    border-color: #535bf2;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.upload-button {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(90deg, #646cff, #9089fc);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 1.1rem;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.upload-button:hover:not(:disabled) {
    background: linear-gradient(90deg, #535bf2, #7b6ef6);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.upload-button:disabled {
    background: #444;
    cursor: not-allowed;
    opacity: 0.7;
    box-shadow: none;
}

/* Loading and error messages */
.loading {
    margin: 2rem 0;
    padding: 1.5rem;
    color: var(--text-muted);
    background-color: rgba(15, 23, 42, 0.4);
    border-radius: 16px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    position: relative;
    animation: pulse 1.5s infinite;
    border: 1px solid rgba(255, 255, 255, 0.05);
    width: 100%;
    max-width: 500px;
    text-align: center;
    font-size: 1.1rem;
}

@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

.loading::before {
    content: '';
    display: block;
    width: 2.5rem;
    height: 2.5rem;
    margin: 0 auto 1rem;
    border: 3px solid rgba(67, 97, 238, 0.2);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s cubic-bezier(0.6, 0.2, 0.4, 0.8) infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.error {
    margin: 2rem 0;
    padding: 1.5rem;
    color: var(--error);
    font-weight: 600;
    background-color: rgba(239, 68, 68, 0.1);
    border-radius: 16px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(239, 68, 68, 0.2);
    width: 100%;
    max-width: 500px;
    position: relative;
    overflow: hidden;
}

.error::before {
    content: '⚠️';
    font-size: 1.5rem;
    display: block;
    margin-bottom: 0.8rem;
}

/* Results */
.results-container {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    padding: 2rem;
    background-color: rgba(42, 42, 42, 0.7);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.results-container h2 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #fff;
    text-align: center;
    font-size: 1.8rem;
    position: relative;
}

.results-container h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #646cff, #9089fc);
    border-radius: 3px;
}

.result-card {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 2rem;
}

.genre-result, .confidence-result {
    padding: 1.5rem;
    background-color: rgba(51, 51, 51, 0.7);
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.genre-result:hover, .confidence-result:hover {
    transform: translateY(-5px);
}

.genre-result h3, .confidence-result h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    color: #aaa;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.genre-name {
    font-size: 2.5rem;
    font-weight: bold;
    color: #fff;
    text-transform: capitalize;
    margin: 0.5rem 0;
    background: linear-gradient(90deg, #646cff, #9089fc);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.confidence-value {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    display: inline-block;
    position: relative;
}

.confidence-value.high {
    color: var(--success);
}

.confidence-value.medium {
    color: var(--warning);
}

.confidence-value.low {
    color: var(--error);
}

.confidence-bar-container {
    width: 100%;
    height: 12px;
    background-color: rgba(15, 23, 42, 0.5);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
    position: relative;
}

.confidence-bar {
    height: 100%;
    border-radius: 10px;
    transition: width 1.5s cubic-bezier(0.19, 1, 0.22, 1);
    position: relative;
    overflow: hidden;
}

.confidence-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    animation: shimmer 2s infinite;
    transform: translateX(-100%);
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

.confidence-bar.high {
    background: linear-gradient(90deg, var(--success), #34d399);
}

.confidence-bar.medium {
    background: linear-gradient(90deg, var(--warning), #fbbf24);
}

.confidence-bar.low {
    background: linear-gradient(90deg, var(--error), #f87171);
}

.result-explanation {
    margin-top: 2.5rem;
    text-align: center;
    color: var(--text-muted);
    font-size: 1.1rem;
    line-height: 1.7;
    padding: 1.5rem;
    background-color: rgba(15, 23, 42, 0.3);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.result-explanation strong {
    color: var(--text-light);
    font-weight: 600;
}

/* Responsive styles */
@media (prefers-color-scheme: light) {
    :root {
        color: #213547;
        background-color: #ffffff;
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }

    .app-container {
        background-color: rgba(255, 255, 255, 0.8);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .file-label {
        background-color: rgba(245, 245, 245, 0.7);
        border-color: #646cff;
    }

    .results-container {
        background-color: rgba(255, 255, 255, 0.8);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .genre-result, .confidence-result {
        background-color: rgba(255, 255, 255, 0.9);
    }

    .genre-name {
        background: linear-gradient(90deg, #646cff, #9089fc);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;
    }

    .genre-result h3, .confidence-result h3 {
        color: #666;
    }

    .result-explanation {
        color: #333;
        background-color: rgba(245, 245, 245, 0.7);
    }

    .loading {
        background-color: rgba(245, 245, 245, 0.7);
    }

    .error {
        background-color: rgba(255, 107, 107, 0.1);
    }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .app-container {
        padding: 1.5rem;
        border-radius: 0;
        min-height: 100vh;
    }

    header h1 {
        font-size: 2rem;
    }

    .file-label {
        padding: 1.5rem 1rem;
    }

    .file-label::before {
        font-size: 2rem;
    }

    .results-container {
        padding: 1.5rem;
    }

    .genre-name {
        font-size: 2rem;
    }

    .confidence-value {
        font-size: 1.5rem;
    }
}

/* Small screens */
@media (max-width: 480px) {
    .app-container {
        padding: 1rem;
    }

    header h1 {
        font-size: 1.8rem;
    }

    header p {
        font-size: 0.9rem;
    }

    .file-label {
        padding: 1.2rem 0.8rem;
    }

    .file-label::before {
        font-size: 1.8rem;
        margin-bottom: 0.8rem;
    }

    .upload-button {
        padding: 0.8rem;
        font-size: 1rem;
    }

    .results-container {
        padding: 1.2rem;
    }

    .genre-result, .confidence-result {
        padding: 1.2rem;
    }

    .genre-name {
        font-size: 1.8rem;
    }

    .confidence-value {
        font-size: 1.3rem;
    }

    .result-explanation {
        font-size: 0.9rem;
    }
}
