import os
import pickle
import numpy as np
import librosa
import hashlib
import json
from pathlib import Path

# List of genres (should match the model's output classes)
GENRES = ['blues', 'classical', 'country', 'disco', 'hiphop', 'jazz', 'metal', 'pop', 'reggae', 'rock']

# Load the trained model
MODEL_PATH = os.path.join(os.path.dirname(__file__), 'genre_classifier.pkl')

# Cache directory for predictions
CACHE_DIR = os.path.join(os.path.dirname(__file__), 'prediction_cache')
os.makedirs(CACHE_DIR, exist_ok=True)

# Global model variable to avoid reloading
_MODEL = None

def extract_features(file_path):
    """Extract features from an audio file with a simpler approach"""
    try:
        # Load audio file
        y, sr = librosa.load(file_path, sr=22050, mono=True, duration=30)

        # Extract MFCCs
        mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
        mfccs_mean = np.mean(mfccs.T, axis=0)

        # Extract Spectral Centroid
        spectral_centroid = librosa.feature.spectral_centroid(y=y, sr=sr)
        spectral_centroid_mean = np.mean(spectral_centroid.T, axis=0)

        # Extract Zero Crossing Rate
        zero_crossing_rate = librosa.feature.zero_crossing_rate(y)
        zero_crossing_rate_mean = np.mean(zero_crossing_rate.T, axis=0)

        # Extract Chroma Features
        chroma = librosa.feature.chroma_stft(y=y, sr=sr)
        chroma_mean = np.mean(chroma.T, axis=0)

        # Extract Spectral Contrast
        spectral_contrast = librosa.feature.spectral_contrast(y=y, sr=sr)
        spectral_contrast_mean = np.mean(spectral_contrast.T, axis=0)

        # Combine all features
        features = np.concatenate((
            mfccs_mean,
            spectral_centroid_mean,
            zero_crossing_rate_mean,
            chroma_mean,
            spectral_contrast_mean
        ))

        return features

    except Exception as e:
        print(f"Error extracting features from {file_path}: {e}")
        return None

def _get_file_hash(file_path):
    """Generate a unique hash for a file based on its content and path"""
    try:
        # Use file path and modification time for quick caching
        file_stat = os.stat(file_path)
        file_info = f"{file_path}_{file_stat.st_size}_{file_stat.st_mtime}"
        return hashlib.md5(file_info.encode()).hexdigest()
    except Exception as e:
        print(f"Error generating file hash: {e}")
        # Fallback to just the path
        return hashlib.md5(file_path.encode()).hexdigest()

def _load_model():
    """Load the model once and cache it in memory"""
    global _MODEL
    if _MODEL is None:
        if os.path.exists(MODEL_PATH):
            print(f"Loading model from {MODEL_PATH}")
            with open(MODEL_PATH, 'rb') as f:
                _MODEL = pickle.load(f)
        else:
            print(f"Model not found at {MODEL_PATH}")
            _MODEL = None
    return _MODEL

def predict_genre(audio_path):
    """
    Predict the genre of an audio file using the trained model
    Uses caching to ensure consistent predictions for the same file
    """
    # Check if the file exists
    if not os.path.exists(audio_path):
        print(f"Audio file not found at {audio_path}")
        return None

    try:
        # Generate a unique hash for this file
        file_hash = _get_file_hash(audio_path)
        cache_file = os.path.join(CACHE_DIR, f"{file_hash}.json")

        # Check if we have a cached prediction
        if os.path.exists(cache_file):
            print(f"Using cached prediction for {audio_path}")
            with open(cache_file, 'r') as f:
                return json.load(f)

        # Load the model (uses cached model if already loaded)
        model = _load_model()
        if model is None:
            return {
                'genre': 'unknown',
                'confidence': 0.0
            }

        # Extract features from the audio file
        print(f"Extracting features from {audio_path}")
        features = extract_features(audio_path)

        if features is not None:
            # Reshape features for prediction
            features = features.reshape(1, -1)

            # Make multiple predictions for stability (ensemble approach)
            # This helps reduce randomness in predictions
            num_predictions = 5
            all_predictions = []

            for _ in range(num_predictions):
                # The model is now a pipeline that includes scaling
                probabilities = model.predict_proba(features)[0]
                all_predictions.append(probabilities)

            # Average the probabilities for more stable results
            avg_probabilities = np.mean(all_predictions, axis=0)

            # Get top 3 predictions with confidences
            top_indices = avg_probabilities.argsort()[-3:][::-1]
            top_genres = [GENRES[i] for i in top_indices]
            top_confidences = [float(avg_probabilities[i]) for i in top_indices]

            # Get the predicted genre (highest confidence)
            predicted_genre = top_genres[0]
            confidence = top_confidences[0]

            print(f"Predicted genre: {predicted_genre} with confidence: {confidence:.4f}")

            # Create the prediction result
            result = {
                'genre': predicted_genre,
                'confidence': confidence,
                'top_predictions': [
                    {'genre': genre, 'confidence': conf}
                    for genre, conf in zip(top_genres, top_confidences)
                ]
            }

            # Cache the prediction
            with open(cache_file, 'w') as f:
                json.dump(result, f)

            return result
        else:
            # If feature extraction failed, return a default response
            print("Feature extraction failed")
            return {
                'genre': 'unknown',
                'confidence': 0.0
            }
    except Exception as e:
        print(f"Error predicting genre: {e}")
        import traceback
        traceback.print_exc()
        return {
            'genre': 'unknown',
            'confidence': 0.0
        }
