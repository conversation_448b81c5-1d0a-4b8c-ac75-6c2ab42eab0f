from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON>art<PERSON>ars<PERSON>, FormParser
from rest_framework.views import APIView

from .models import AudioFile, Prediction
from .serializers import AudioFileSerializer, PredictionSerializer, PredictionResultSerializer

from ml_model.model import predict_genre, GENRES

class AudioFileUploadView(APIView):
    parser_classes = (MultiPartParser, FormParser)
    
    def post(self, request, *args, **kwargs):
        """Handle audio file upload and make predictions"""
        serializer = AudioFileSerializer(data=request.data)
        
        if serializer.is_valid():
            # Save the uploaded file
            audio_file = serializer.save()
            
            # Get the file path
            file_path = audio_file.file.path
            
            # Make prediction
            prediction_result = predict_genre(file_path)
            
            if prediction_result:
                # Save the prediction to the database
                prediction = Prediction.objects.create(
                    audio_file=audio_file,
                    genre=prediction_result['genre'],
                    confidence=prediction_result['confidence']
                )
                
                # Return the prediction result
                result_serializer = PredictionResultSerializer(prediction_result)
                return Response(result_serializer.data, status=status.HTTP_201_CREATED)
            else:
                return Response(
                    {'error': 'Failed to make prediction'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def get_genres(request):
    """Return the list of available genres"""
    return Response({'genres': GENRES})
