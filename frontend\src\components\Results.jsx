import './Results.css';

const Results = ({ result }) => {
  const { genre, confidence, top_predictions } = result;

  // Format confidence as percentage
  const confidencePercent = (confidence * 100).toFixed(2);

  // Determine confidence level class
  let confidenceClass = 'low';
  if (confidence >= 0.7) {
    confidenceClass = 'high';
  } else if (confidence >= 0.4) {
    confidenceClass = 'medium';
  }

  return (
    <div className="results-container">
      <h2>Classification Results</h2>

      <div className="result-card">
        <div className="genre-result">
          <h3>Predicted Genre</h3>
          <div className="genre-name">{genre}</div>
        </div>

        <div className="confidence-result">
          <h3>Confidence</h3>
          <div className={`confidence-value ${confidenceClass}`}>
            {confidencePercent}%
          </div>
          <div className="confidence-bar-container">
            <div
              className={`confidence-bar ${confidenceClass}`}
              style={{ width: `${confidencePercent}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Show top predictions if available */}
      {top_predictions && top_predictions.length > 1 && (
        <div className="top-predictions">
          <h3>Alternative Genres</h3>
          <div className="predictions-list">
            {top_predictions.slice(1).map((pred, index) => (
              <div key={index} className="prediction-item">
                <span className="prediction-genre">{pred.genre}</span>
                <div className="prediction-confidence-container">
                  <div
                    className="prediction-confidence-bar"
                    style={{ width: `${(pred.confidence * 100).toFixed(2)}%` }}
                  ></div>
                  <span className="prediction-confidence-value">
                    {(pred.confidence * 100).toFixed(2)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="result-explanation">
        <p>
          The model predicts this audio is <strong>{genre}</strong> music with
          a confidence of <strong>{confidencePercent}%</strong>.
        </p>
        <p className="consistency-note">
          <strong>Note:</strong> This model now provides consistent predictions for the same audio file.
        </p>
      </div>
    </div>
  );
};

export default Results;
