<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Music Genre Classifier | AI-Powered Audio Analysis</title>
    <meta name="description" content="Classify music into genres using advanced machine learning algorithms. Upload your audio files and get instant genre predictions.">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🎵</text></svg>">
</head>
<body>
    <div class="app-container">
        <header>
            <div class="logo-container">
                <div class="logo">
                    <span class="logo-icon">🎵</span>
                    <span class="logo-text">MusicAI</span>
                </div>
            </div>
            <h1>Music Genre Classifier</h1>
            <p>Upload an audio file to classify its genre using our advanced machine learning algorithm</p>
            <div class="header-actions">
                <button class="action-button info-button" id="about-button">
                    <span class="button-icon">ℹ️</span> About
                </button>
                <button class="action-button help-button" id="help-button">
                    <span class="button-icon">❓</span> How It Works
                </button>
            </div>
        </header>

        <main>
            <div class="file-upload">
                <div class="section-header">
                    <h2>Upload Your Audio</h2>
                    <p class="section-description">Supported formats: MP3, WAV, M4A (max 10MB)</p>
                </div>

                <form id="upload-form">
                    <div class="file-input-container">
                        <input
                            type="file"
                            id="file-input"
                            class="file-input"
                            accept="audio/*"
                        >
                        <label for="file-input" class="file-label">
                            Choose an audio file
                        </label>
                    </div>

                    <div id="file-info" style="display: none;">
                        <div class="file-details">
                            <div class="file-info-header">Selected File:</div>
                            <span id="file-name"></span>
                            <span id="file-size"></span>
                        </div>
                        <button type="button" id="clear-file" class="clear-button">
                            <span class="button-icon">🗑️</span> Clear
                        </button>
                    </div>

                    <button type="submit" class="upload-button" disabled>
                        <span class="button-icon">🔍</span> Classify Genre
                    </button>
                </form>
            </div>

            <div id="loading" class="loading" style="display: none;">
                <div class="loading-spinner"></div>
                Processing your audio file...
                <div class="loading-details">Analyzing audio patterns and features</div>
            </div>

            <div id="error" class="error" style="display: none;"></div>

            <div id="results" class="results-container" style="display: none;">
                <div class="results-header">
                    <h2>Classification Results</h2>
                    <div class="results-timestamp" id="results-timestamp"></div>
                </div>

                <div class="result-card">
                    <div class="genre-result">
                        <h3>Predicted Genre</h3>
                        <div id="genre-name" class="genre-name"></div>
                        <div id="genre-icon" class="genre-icon"></div>
                    </div>

                    <div class="confidence-result">
                        <h3>Confidence Level</h3>
                        <div id="confidence-value" class="confidence-value"></div>
                        <div class="confidence-bar-container">
                            <div id="confidence-bar" class="confidence-bar"></div>
                        </div>
                    </div>
                </div>

                <div class="result-explanation">
                    <p id="result-explanation"></p>
                </div>

                <div class="results-actions">
                    <button id="try-again" class="try-again-button">
                        <span class="button-icon">🔄</span> Try Another File
                    </button>
                    <button id="share-results" class="share-button">
                        <span class="button-icon">📤</span> Share Results
                    </button>
                </div>
            </div>

            <div class="supported-genres">
                <h3>Supported Genres</h3>
                <div class="genre-list">
                    <span class="genre-tag">Rock</span>
                    <span class="genre-tag">Pop</span>
                    <span class="genre-tag">Hip Hop</span>
                    <span class="genre-tag">Jazz</span>
                    <span class="genre-tag">Classical</span>
                    <span class="genre-tag">Country</span>
                    <span class="genre-tag">Metal</span>
                    <span class="genre-tag">Blues</span>
                    <span class="genre-tag">Reggae</span>
                    <span class="genre-tag">Disco</span>
                </div>
            </div>

            <div class="features-section">
                <h3>Key Features</h3>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🧠</div>
                        <h4>AI-Powered</h4>
                        <p>Advanced machine learning algorithms trained on thousands of songs</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h4>Fast Analysis</h4>
                        <p>Get results in seconds, no matter the length of your audio file</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔒</div>
                        <h4>Privacy First</h4>
                        <p>Your audio files are processed securely and never shared</p>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Music Genre Classifier</h4>
                    <p>An AI-powered tool for music genre classification using advanced machine learning algorithms.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="#" id="privacy-link">Privacy Policy</a></li>
                        <li><a href="#" id="terms-link">Terms of Service</a></li>
                        <li><a href="#" id="contact-link">Contact Us</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Powered by Django and Machine Learning</p>
                <p class="copyright">© 2025 Music Genre Classifier | All Rights Reserved</p>
            </div>
        </footer>
    </div>

    <!-- Modal dialogs -->
    <div id="about-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>About Music Genre Classifier</h2>
            <p>This application uses machine learning to analyze audio files and predict their musical genre. Our model has been trained on thousands of songs across multiple genres to provide accurate predictions.</p>
            <p>Simply upload your audio file, and our AI will analyze its patterns, rhythms, and acoustic features to determine the most likely genre.</p>
        </div>
    </div>

    <div id="help-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>How It Works</h2>
            <ol>
                <li><strong>Upload:</strong> Select an audio file (MP3, WAV, or M4A format)</li>
                <li><strong>Process:</strong> Our AI analyzes the audio patterns and features</li>
                <li><strong>Results:</strong> View the predicted genre and confidence level</li>
            </ol>
            <p>For best results, upload high-quality audio files with minimal background noise.</p>
        </div>
    </div>

    <script src="{% static 'js/app.js' %}"></script>
</body>
</html>
