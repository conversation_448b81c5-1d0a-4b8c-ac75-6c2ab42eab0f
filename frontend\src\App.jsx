import { useState } from 'react'
import './App.css'
import FileUpload from './components/FileUpload'
import Results from './components/Results'

function App() {
  const [result, setResult] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  return (
    <div className="app-container">
      <header>
        <h1>Music Genre Classifier</h1>
        <p>Upload an audio file to classify its genre</p>
      </header>

      <main>
        <FileUpload 
          setResult={setResult} 
          setLoading={setLoading} 
          setError={setError} 
        />
        
        {loading && <div className="loading">Processing your audio file...</div>}
        
        {error && <div className="error">{error}</div>}
        
        {result && <Results result={result} />}
      </main>

      <footer>
        <p>Powered by Django and React</p>
      </footer>
    </div>
  )
}

export default App
